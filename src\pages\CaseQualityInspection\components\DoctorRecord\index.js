import React, { Component } from 'react';
import router from 'umi/router';
import { Table } from 'antd';
import * as echarts from 'echarts';
import styles from './index.less';

function getLineOption(doctorNames, avgScores, redLineRates) {
  return {
    tooltip: {
      trigger: 'axis',
      formatter(params) {
        let result = `${params[0].name}<br/>`;

        // 按指定顺序排序：平均得分在前，红线病例检出率在后
        const sortedParams = params?.sort((a, b) => {
          if (a.seriesName === '平均得分') return -1;
          if (b.seriesName === '平均得分') return 1;
          return 0;
        });

        sortedParams?.forEach(item => {
          if (item.seriesName === '平均得分') {
            result += `${item.marker}${item.seriesName}: ${item.value.toFixed(0)}分<br/>`;
          } else {
            result += `${item.marker}${item.seriesName}: ${item.value.toFixed(0)}%<br/>`;
          }
        });
        return result;
      },
    },
    legend: {
      data: ['平均得分', '红线病历检出率'],
      bottom: 0,
      icon: 'line',
      itemWidth: 16,
      itemHeight: 4,
      textStyle: { fontSize: 14 },
    },
    grid: { left: 30, right: 30, top: 40, bottom: 40, containLabel: true },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: doctorNames,
      axisLine: { lineStyle: { color: '#e5e5e5' } },
      axisTick: { show: false },
      axisLabel: { color: '#666', fontSize: 14 },
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 120,
      splitLine: { lineStyle: { color: '#f0f0f0', type: 'dashed' } },
      axisLabel: { color: '#bfbfbf', fontSize: 12 },
    },
    series: [
      {
        name: '红线病历检出率',
        type: 'line',
        data: redLineRates,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: { color: '#ff4d4f' },
        lineStyle: { color: '#ff4d4f', width: 2 },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{ offset: 0, color: '#fff1f0' }, { offset: 1, color: '#fff' }],
          },
        },
        z: 2,
      },
      {
        name: '平均得分',
        type: 'line',
        data: avgScores,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: { color: '#1890ff' },
        lineStyle: { color: '#1890ff', width: 2 },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{ offset: 0, color: '#e6f7ff' }, { offset: 1, color: '#fff' }],
          },
        },
        z: 1,
      },
    ],
  };
}

class DoctorRecord extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.echartsRef = React.createRef();
  }

  componentDidMount() {
    this.renderEcharts();
    window.addEventListener('resize', this.resizeEcharts);
  }

  componentDidUpdate() {
    this.renderEcharts();
  }

  componentWillUnmount() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
    window.removeEventListener('resize', this.resizeEcharts);
  }

  // 处理折线图数据
  processChartData = () => {
    const { doctorStats = [] } = this.props;

    if (doctorStats?.length === 0) {
      // 如果没有数据，返回默认空数据
      return {
        doctorNames: [],
        avgScores: [],
        redLineRates: [],
      };
    }

    const doctorNames = doctorStats?.map(item => item.statKey);
    const avgScores = doctorStats?.map(item => item.avgScore);
    const redLineRates = doctorStats?.map(item => item.problemProbability * 100); // 乘以100

    return {
      doctorNames,
      avgScores,
      redLineRates,
    };
  };

  renderEcharts = () => {
    if (!this.echartsRef.current) return;
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
    this.chartInstance = echarts.init(this.echartsRef.current);

    const { doctorNames, avgScores, redLineRates } = this.processChartData();
    this.chartInstance.setOption(getLineOption(doctorNames, avgScores, redLineRates));
  };

  resizeEcharts = () => {
    if (this.chartInstance) {
      this.chartInstance.resize();
    }
  };

  // 根据父组件传入的doctorScoreHeaders生成表格列配置
  generateColumns = () => {
    const { doctorScoreHeaders = [] } = this.props;

    const columns = [
      {
        title: '',
        dataIndex: 'doctorName',
        key: 'doctorName',
        align: 'center',
        width: 80,
      },
    ];

    // 根据doctorScoreHeaders动态生成列
    doctorScoreHeaders?.forEach((header, index) => {
      columns.push({
        title: header,
        dataIndex: `score${index}`,
        key: `score${index}`,
        align: 'center',
        width: 100, // 设置固定宽度确保对齐
        render: text => <span className={styles.link}>{text}</span>,
      });
    });

    return columns;
  };

  // 处理表格数据源
  generateDataSource = () => {
    const { doctorScoreStats = [] } = this.props;

    return doctorScoreStats?.map((item, index) => {
      const dataItem = {
        key: `${index + 1}`,
        doctorName: item.doctorName || `医生${index + 1}`,
      };

      // 处理每一项数据：乘100再加上%符号
      if (item.groupDatas && Array.isArray(item.groupDatas)) {
        item.groupDatas?.forEach((value, dataIndex) => {
          dataItem[`score${dataIndex}`] = `${(value * 100).toFixed(0)}%`;
        });
      }

      return dataItem;
    });
  };

  render() {
    const columns = this.generateColumns();
    const dataSource = this.generateDataSource();

    return (
      <div className={styles.doctorRecordCom}>
        <div className={styles.cardGroup}>
          {/* 左侧表格卡片 */}
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <span className={styles.cardTitle}>医生病历质量分布</span>
              <span
                className={styles.cardLink}
                onClick={() => router.push('/CaseQualityInspection/CaseQualityDistribution')}
              >
                查看详情 &gt;
              </span>
            </div>
            <div className={styles.tableWrapper}>
              <Table
                columns={columns}
                dataSource={dataSource}
                pagination={false}
                bordered={false}
                rowClassName={() => styles.tableRow}
                size="middle"
                scroll={{ y: 230 }}
              />
            </div>
          </div>
          {/* 右侧折线图卡片 */}
          <div className={styles.card}>
            <div className={styles.cardHeader}>
              <span className={styles.cardTitle}>医生病历质量情况</span>
              <span
                className={styles.cardLink}
                onClick={() => router.push('/CaseQualityInspection/CaseQualityDistribution')}
              >
                查看详情 &gt;
              </span>
            </div>
            <div className={styles.chartWrapper}>
              <div ref={this.echartsRef} style={{ width: '100%', height: 280 }} />
            </div>
          </div>
        </div>
      </div>
    );
  }
}

export default DoctorRecord;
