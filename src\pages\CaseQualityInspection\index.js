/**
 * @Description: 病历质检
 * @author: sy
 */
import React, { Component } from 'react';
import { connect } from 'dva';
import { Spin, Select, Radio } from 'antd';
import Dashboard from './components/Dashboard';
import Overview from './components/Overview';
import Redline from './components/Redline';
import DoctorRecord from './components/DoctorRecord';

import styles from './index.less';

@connect(({ loading, caseQualityInspection }) => ({
  loading,
  caseQualityInspection,
}))
class Index extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedTime: '', // 默认选中“月” 或者 “周”
      selectedRangeTime: '0', // 0:当前周期，-1:上一个周期，-2：上上周期
      emrNum: 0, // 病历数量
      problemNum: 0, // 红线问题数量
      avgScore: 0, // 平均分
      problemPercent: 0, // 红线问题百分比
      avgScoreGrowth: 0,
      problemPercentGrowth: 0,
      dailyStats: [], // 本月质检一览
      problemStats: [], // 红线检出问题类型
      subjectStats: [], // 检出红线的学科分布情况
      scoreStats: [], // 诊所病历质量分布
      doctorScoreHeaders: [], // 医生病历质量分布 -表头
      doctorScoreStats: [], // 医生病历质量分布
      doctorStats: [], // 医生统计数据
      aiAnalysis: '', // AI智能推荐
    };
  }

  componentDidMount() {
    const selectedTime = localStorage.getItem('caseQualityInspectionSelectedTime');
    const selectedRangeTime = localStorage.getItem('caseQualityInspectionSelectedRangeTime');
    if (!selectedTime) {
      localStorage.setItem('caseQualityInspectionSelectedTime', 'MONTH');
      localStorage.setItem('caseQualityInspectionSelectedRangeTime', '0');
      this.setState({
        selectedTime: 'MONTH',
        selectedRangeTime: '0',
      });
    } else {
      this.setState({
        selectedTime,
        selectedRangeTime,
      });
    }
    this.getBoardDetailData();
  }

  // 获取数据
  getBoardDetailData = () => {
    const { dispatch } = this.props;

    const params = {
      organizationId: localStorage.getItem('organizationInfoId'),
      tenantId: localStorage.getItem('tenantId'),
      userId: localStorage.getItem('id'),
      userName: localStorage.getItem('userName'),
      statTimeType: localStorage.getItem('caseQualityInspectionSelectedTime'),
      statTimeOffset: localStorage.getItem('caseQualityInspectionSelectedRangeTime'),
    };

    dispatch({
      type: 'caseQualityInspection/getBoardDetail',
      payload: params,
    })
      .then(res => {
        if (res && res.code === 200) {
          const {
            emrNum,
            problemNum,
            avgScore,
            problemPercent,
            dailyStats,
            problemStats,
            subjectStats,
            scoreStats,
            doctorScoreHeaders,
            doctorScoreStats,
            doctorStats,
            avgScoreGrowth,
            problemPercentGrowth,
            aiAnalysis,
          } = res.data;
          this.setState({
            emrNum,
            problemNum,
            avgScore,
            problemPercent,
            avgScoreGrowth,
            problemPercentGrowth,
            dailyStats,
            problemStats,
            subjectStats,
            scoreStats,
            doctorScoreHeaders,
            doctorScoreStats,
            doctorStats,
            aiAnalysis,
          });
        }
      })
      .catch(err => {
        console.error(err);
      });
  };

  handleTimeChange = e => {
    const value = e.target.value;
    this.setState({
      selectedTime: value,
      selectedRangeTime: '0' // 重置为默认选项
    }, () => {
      localStorage.setItem('caseQualityInspectionSelectedTime', value);
      localStorage.setItem('caseQualityInspectionSelectedRangeTime', '0');
      this.getBoardDetailData();
    });
  };

  handleRangeChange = value => {
    this.setState({ selectedRangeTime: value }, () => {
      localStorage.setItem('caseQualityInspectionSelectedRangeTime', value);
      this.getBoardDetailData();
    });
  };

  render() {
    const {
      selectedTime,
      selectedRangeTime,
      emrNum,
      problemNum,
      avgScore,
      problemPercent,
      dailyStats,
      problemStats,
      subjectStats,
      scoreStats,
      doctorScoreHeaders,
      doctorScoreStats,
      doctorStats,
      avgScoreGrowth,
      problemPercentGrowth,
      aiAnalysis,
    } = this.state;
    const { loading } = this.props;
    return (
      <Spin spinning={!!loading.effects['caseQualityInspection/getBoardDetail']}>
        <div className={styles.cqi_header}>
          <span className={styles.cqi_title}>电子病历质量检查</span>

          <div className="cqi_time">
            <Radio.Group
              value={selectedTime}
              buttonStyle="solid"
              size="small"
              onChange={v => this.handleTimeChange(v)}
            >
              <Radio.Button value="MONTH">月</Radio.Button>
              <Radio.Button value="WEEK">周</Radio.Button>
            </Radio.Group>

            {/* 时间筛选下拉框，默认选中“本月” 或者 “本周” */}

            <Select value={selectedRangeTime} onChange={v => this.handleRangeChange(v)}>
              <Select.Option value="0">本月</Select.Option>
              <Select.Option value="-1">上月</Select.Option>
              <Select.Option value="-2">上上月</Select.Option>
            </Select>
          </div>
        </div>
        <Dashboard
          selectedTime={selectedTime}
          emrNum={emrNum}
          avgScore={avgScore}
          problemPercent={problemPercent}
          avgScoreGrowth={avgScoreGrowth}
          problemPercentGrowth={problemPercentGrowth}
          aiAnalysis={aiAnalysis}
        />
        <Overview selectedTime={selectedTime} dailyStats={dailyStats} />
        <Redline
          problemNum={problemNum}
          problemStats={problemStats}
          subjectStats={subjectStats}
          scoreStats={scoreStats}
        />
        <DoctorRecord
          doctorScoreHeaders={doctorScoreHeaders}
          doctorScoreStats={doctorScoreStats}
          doctorStats={doctorStats}
        />
      </Spin>
    );
  }
}

export default Index;
