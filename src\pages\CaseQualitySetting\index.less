.settingContainer {
  // padding: 24px;
  // background: #f5f5f5;
  min-height: 100vh;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
      color: #071E3F;
      line-height: 25px;
    }
  }

  .settingCard {
    margin-bottom: 8px;
    border-radius: 6px;

    :global(.ant-card-head) {
      border-bottom: 1px solid #f0f0f0;
      padding: 0 24px;
      min-height: 48px;

      :global(.ant-card-head-title) {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
        padding: 12px 0;
      }
    }

    :global(.ant-card-body) {
      padding: 24px;
    }
  }

  .settingItem {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    .settingLabel {
      font-size: 14px;
      font-weight: 500;
      color: #262626;
      margin-right: 16px;
      min-width: 80px;
    }

    .settingDesc {
      font-size: 14px;
      color: #8c8c8c;
      line-height: 1.5;
    }
  }

  .settingContent {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .fieldLabel {
      font-size: 14px;
      color: #262626;
      margin-right: 12px;
      min-width: 80px;
    }
  }

  .tableWrapper {
    margin-top: 16px;

    .scoreConfigTable {
      :global(.ant-table-thead > tr > th) {
        background: #fafafa;
        font-weight: 500;
        color: #262626;
        border-bottom: 1px solid #f0f0f0;
      }

      :global(.ant-table-tbody > tr > td) {
        border-bottom: 1px solid #f0f0f0;
        padding: 12px 16px;
      }

      :global(.ant-table-row-expand-icon) {
        color: #1890ff;
      }

      :global(.ant-table-expanded-row > td) {
        background: #fafafa;
      }
    }
  }

  .switchWrapper {
    display: flex;
    align-items: center;

    .switchText {
      margin-left: 8px;
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .redlineRules {
    margin-top: 24px;
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    background: #fff;

    .rulesHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
      border-radius: 6px 6px 0 0;

      .rulesTitle {
        font-size: 14px;
        font-weight: 500;
        color: #262626;
      }
    }

    .ruleItem {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 16px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .ruleText {
        flex: 1;
        font-size: 14px;
        color: #262626;
        line-height: 1.5;
        margin-right: 16px;
      }

      .ruleStatus {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .switchText {
          margin-left: 8px;
          font-size: 12px;
          color: #8c8c8c;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settingContainer {
    padding: 16px;

    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .settingItem {
      flex-direction: column;
      align-items: flex-start;

      .settingLabel {
        margin-bottom: 8px;
        margin-right: 0;
      }
    }

    .settingContent {
      flex-direction: column;
      align-items: flex-start;

      .fieldLabel {
        margin-bottom: 8px;
        margin-right: 0;
      }
    }

    .redlineRules {
      .ruleItem {
        flex-direction: column;
        align-items: flex-start;

        .ruleText {
          margin-right: 0;
          margin-bottom: 12px;
        }
      }
    }
  }
}