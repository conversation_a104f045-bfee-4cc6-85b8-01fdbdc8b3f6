import { routerRedux } from 'dva/router';
import { stringify } from "qs";
import { fakeAccountLogin, getToken, OldchangePssword, FindAccountNum, getRedioConfig } from '@/services/api';
import { setAuthority } from '@/utils/authority';
import { Modal, message } from 'antd';
import router from 'umi/router';
import { getPageQuery, getAES, setOrganizationInfo, removeOrganizationInfo, CheckPostern, goBack5i5yaHome, CheckArrailRytime } from '@/utils/utils';
import { reloadAuthorized } from '@/utils/Authorized';
function setServicelocalStorageInfo(content) {
  // 拿到每个权限的id通过 item.platformId
  let role = [];
  content.userRoleList && content.userRoleList.map(item => {
    role.push({
      roleCode: item.roleCode,
      roleName: item.roleName,
      tenantId: item.tenantId
    })
  })
  localStorage.setItem('doctorIdentification', content.doctorIdentification)  // 1医生  2非医生
  localStorage.setItem("accountNumber", content.accountNumber); // 账号
  localStorage.setItem("id", content.id); // 用户id
  localStorage.setItem("tenantId", content.tenantId); // 租户id
  localStorage.setItem("phone", content.phone); // 用户手机号
  localStorage.setItem("email", content.email); // 用户邮箱
  localStorage.setItem("avatar", content.headUrlView); // 用户头像
  localStorage.setItem("userName", content.userName); // 用户名字
  localStorage.setItem("isResources", content.isResources); // 是否是资源  可以进入其他设置  1是
  localStorage.setItem("fourCustomer", content.fourCustomer); // 是否是400客服
}

export default {
  namespace: 'login',

  state: {
    status: undefined,
  },
  effects: {
    // 老用户更改密码
    *getOldPassWord({ payload }, { call, put }) {
      const response = yield call(OldchangePssword, payload);
      if (response && response.content) {
        router.push('/user/login');
        message.success('修改成功！');
      } else {
        message.error('修改失败！')
      }
    },
    // 验证账号是否已经完善信息
    *getAccountNum({ payload }, { call, put }) {
      const response = yield call(FindAccountNum, payload);
      if (response && response.code && response.code == 200) {
        return response
      }
    },

    *login({ payload }, { call, put }) {
      // 优先获取本地存储的租户ID,如果没有从地址栏获取
      const tenantId = localStorage.getItem("tenantId") ? localStorage.getItem("tenantId") : getPageQuery().tenantId;
      if (!tenantId || tenantId == "null" || tenantId == "undefined") {
        Modal.error({
          title: '信息错误',
          content: '租户id不存在',
        });
        return;
      } else {
        // 重新登录，本地存储租户id
        localStorage.setItem('tenantId', tenantId);
      }

      var formData = new FormData();
      // pwd处理
      const wordpass = payload.password;
      const AgetES = getAES(wordpass, 'arrail-dentail&2', 'arrail-dentail&3');
      payload.password = AgetES;
      formData.append("grant_type", "password");
      Object.keys(payload).map((v) => {
        if (v == "username") {
          console.log(CheckPostern())
          if (!CheckPostern()) {
            formData.append("username", `${payload[v]}/${tenantId}/f`);
          } else {
            formData.append("username", `${payload[v]}/${tenantId}`);
          }
        } else {
          formData.append(v, payload[v]);
        }
      });
      const token = yield call(getToken, formData);
      if (token.access_token) {
        console.log('111113');
        localStorage.setItem('access_token', token.access_token); // 保存access_token
        localStorage.setItem('token_username', payload.username) // 账户名称
        const response = yield call(fakeAccountLogin, payload); // 获取用户信息
        // 添加接口报错提示～！
        if (response == undefined) {
          message.error('数据加载失败！');
          return false;
        }

        // 正常登录
        if (response.content && response.content.status == 1) {
          console.log('1111112')
          Modal.error({
            title: '提醒',
            content: '您的账号已被禁用，如需启用请联系管理员!',
          });
          return false;
        }
        // 判断是否是老用户
        if (response.content && response.content.isOldUser == 'false') {
          router.push(`/user/oldlogin?userId=${response.content.id}&username=${payload.username}`)
          return false;
        }
        if (response.code == 200) {
          setOrganizationInfo(response.content.organizationInfo);  // 保存所属机构信息
          localStorage.setItem('organizationInfoId', response.content.organizationInfo && response.content.organizationInfo.id); // 保存机构Id
          localStorage.setItem('organizationSize', response.content.organizationSize); // 用户下有多少机构
          // 拿到每个权限的id通过 item.platformId
          let scope = [];

          response.content.menuInfoList && response.content.menuInfoList.map((item) => {
            scope.push(item.platformId)
          })

          console.log(scope);
          setAuthority(scope);
          // 角色
          let role = [];
          response.content.userRoleList && response.content.userRoleList.map(item => {

            role.push({
              roleCode: item.roleCode,
              roleName: item.roleName,
              tenantId: item.tenantId,
              roleId: item.roleId
            })
          })

          // 登录呼叫中心
          // moorCall.moortools.m7BeginLogon('8000@ruier', '8000', 'Local', '0');
          localStorage.setItem('scope', JSON.stringify(scope)) // 权限
          localStorage.setItem('role', JSON.stringify(role)) // 多角色
          localStorage.setItem('doctorIdentification', response.content.doctorIdentification)  // 1医生  2非医生
          localStorage.setItem("accountNumber", response.content.accountNumber); // 账号
          localStorage.setItem("id", response.content.id); // 用户id
          localStorage.setItem("tenantId", response.content.tenantId); // 租户id
          localStorage.setItem("phone", response.content.phone); // 用户手机号
          localStorage.setItem("email", response.content.email); // 用户邮箱
          localStorage.setItem("avatar", response.content.headUrlView); // 用户头像
          localStorage.setItem("userName", response.content.userName); // 用户名字
          localStorage.setItem("isResources", response.content.isResources); // 是否是资源  可以进入其他设置  1是
          localStorage.setItem("fourCustomer", response.content.fourCustomer); // 是否是400客服
          localStorage.setItem('tenantName', response.content.tenantName);     // 品牌名称
          localStorage.setItem('isRead', response.content.isRead);     // 更新系统后是否首次登录
          localStorage.setItem('isNeedSignRecord', response.content.organizationInfo && response.content.organizationInfo.isNeedSignRecord);     // 判断到诊图例小程序预约患者图例是否显示  0需要 1不需要
          localStorage.setItem('fourOnState',response.content.fourOnState);    //   400列表开启状态(0：关闭，1：开启)
          if (response.content.fourCustomer == 1) {
            if (scope && scope.length > 0) {
              let path = Math.min.apply(Math, scope);
              switch (path) {
                case 76:
                  router.replace("/customerService/appointment");
                  break;
                case 77:
                  router.replace("/customerService/patient");
                  break;
                case 78:
                  router.replace("/customerService/set");
                  break;
                case 79:
                  router.replace("/customerService/welfare");
                  break;
                case 108:
                  router.replace("/customerService/complaint/list");
                  break;
                case 120:
                  router.replace("/customerService/searchChargeItem");
                  break;
                default:
                  break;
              }

            } else {
              Modal.error({
                title: '暂无权限',
                content: '该用户暂无权限，请联系400管理员!',
                okText: "确定"
              });
              return false;
            }

          } else {
            // 判读是否需要人脸识别
            if (response.content.wjUrl != null && response.content.wjUrl != "") {
              window.location.replace(response.content.wjUrl);
            } else {
              router.replace("/")
            }
          }

        } else {
          Modal.error({
            title: '信息错误',
            content: response.msg,
            okText: "确定"
          });
        }
      } else {
        Modal.error({
          title: '信息错误',
          content: '账号或密码错误，不可登录!',
          okText: "确定"
        });
        return false;
      }

      // Login successfully
      reloadAuthorized();
    },

    // *getCaptcha({ payload }, { call }) {
    //   yield call(getFakeCaptcha, payload);
    // },

    *logout(_, { put }) {
      const tenantId = localStorage.getItem('tenantId');
      localStorage.removeItem('access_token'); // 保存access_token
      localStorage.removeItem('token_username'); // 账户名称
      localStorage.removeItem('phoneName'); // 账户名称
      localStorage.removeItem("fourCustomer"); // 是否是400客服
      let scope = [];
      setAuthority(scope);
      removeOrganizationInfo(); // 删除机构信息
      // reloadAuthorized();
      /*yield put(
        routerRedux.push({
          pathname: '/user/login',
          // search: stringify({
          //   tenantId: tenantId,
          // }),
        })
      );*/
      localStorage.removeItem('CustomDrawerList');   // 删除客户档案自行列表
      localStorage.removeItem('role') // 角色
      localStorage.removeItem('doctorIdentification')  // 1医生  2非医生
      localStorage.removeItem("accountNumber"); // 账号
      localStorage.removeItem("id"); // 用户id
      // localStorage.removeItem("tenantId"); // 租户id
      localStorage.removeItem("phone"); // 用户手机号
      localStorage.removeItem("email"); // 用户邮箱
      localStorage.removeItem("avatar"); // 用户头像
      localStorage.removeItem("userName"); // 用户名字
      localStorage.removeItem('organizationInfoId'); // 机构Id
      localStorage.removeItem("isResources"); // 是否是资源  可以进入其他设置  1是
      localStorage.removeItem('organizationSize'); // 用户下有多少机构
      localStorage.removeItem('socket');
      localStorage.removeItem("firstLogin");
      localStorage.removeItem("fourCustomer");
      localStorage.removeItem("organizationInfoJsonCS");
      localStorage.removeItem("saveOnTimeBlankClickByFull");
      localStorage.removeItem('tenantName');   // 品牌名称
      localStorage.removeItem('isRead');   // 品牌名称
      localStorage.removeItem('userRoleList');   // 权限
      localStorage.removeItem('scope');   // 权限
      localStorage.removeItem('isNeedSignRecord');     // 判断到诊图例小程序预约患者图例是否显示  0需要 1不需要
      localStorage.removeItem('submitList');         // 清除常用菜单配置
      localStorage.removeItem('fourOnState');          //   400列表开启状态(0：关闭，1：开启)
      localStorage.removeItem('tenantId');   // 退出清除本地存储的租户id
      localStorage.removeItem('userTenantId');   // 退出清除本地存储的租户id（登录用户的租户Id，登录第三方400时用的）
      localStorage.removeItem('UidKey');   // 退出清除本地存储的key
      localStorage.removeItem('minimizeStorageKey');   // 退出清除本地存储的预约最小化数据
      localStorage.removeItem('minimizeVisitStorageKey');   // 退出清除本地存储的回访任务最小化数据
      localStorage.removeItem('caseQualityInspectionSelectedTime');  // 退出清除本地存储的质检时间
      localStorage.removeItem('caseQualityCheckDateCleared');  // 退出清除本地存储的默认筛选时间
      localStorage.removeItem('caseQualityDistributionDateCleared');  // 退出清除本地存储的默认筛选时间
      localStorage.removeItem('caseQualityRedLineDateCleared');  // 退出清除本地存储的默认筛选时间
      const URL = goBack5i5yaHome();
      // localStorage.removeItem('AllowAdmin');   // 是不是云平台登录的
      window.location.replace(URL);
    },

    // 获取升级广播配置信息
    *getRedioConfig({ payload }, { call }) {
      const res = yield call(getRedioConfig);
      return res
    }

  },

  reducers: {
    changeLoginStatus(state, { payload }) {
      setAuthority(payload.currentAuthority);
      return {
        ...state,
        status: payload.status,
        type: payload.type,
      };
    },
  },
};
