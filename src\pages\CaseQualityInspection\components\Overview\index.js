// src/pages/CaseQualityInspection/components/Overview/index.js
import React from 'react';
import router from 'umi/router';
import * as echarts from 'echarts';
import styles from './index.less';
import nextIcon from '@/assets/CaseQualityInspection/next.png';

class Overview extends React.Component {
  constructor(props) {
    super(props);
    this.chartRef = React.createRef();
    this.handleResize = this.handleResize.bind(this);
    this.state = {
      currentPage: 1,
      pageSize: 15, // 本月每页显示15条数据
    };
  }

  componentDidMount() {
    this.initChart();
    window.addEventListener('resize', this.handleResize);
  }

  componentDidUpdate(prevProps, prevState) {
    // 当 selectedTime 或 dailyStats 改变时重新渲染图表
    const { selectedTime, selectedRangeTime, dailyStats } = this.props;
    const { currentPage } = this.state;

    // 当数据源改变时，重置到第一页
    if (
      prevProps.selectedTime !== selectedTime ||
      prevProps.selectedRangeTime !== selectedRangeTime ||
      prevProps.dailyStats !== dailyStats
    ) {
      // 如果当前不是第一页，需要重置
      if (currentPage !== 1) {
        // 使用异步方式避免在 componentDidUpdate 中直接调用 setState
        setTimeout(() => {
          this.setState({ currentPage: 1 });
        }, 0);
      } else {
        this.initChart();
      }
    } else if (prevState.currentPage !== currentPage) {
      // 仅页码改变时重新渲染图表
      this.initChart();
    }
  }

  componentWillUnmount() {
    if (this.chartInstance) {
      this.chartInstance.dispose();
    }
    window.removeEventListener('resize', this.handleResize);
  }

  // 获取图表数据
  getChartData() {
    const { dailyStats = [], selectedTime } = this.props;
    const { currentPage, pageSize } = this.state;

    let dataToShow = dailyStats;

    // 只有本月才进行分页处理
    if (selectedTime === 'MONTH' && dailyStats?.length > pageSize) {
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      dataToShow = dailyStats.slice(startIndex, endIndex);
    }

    // 转换数据格式
    const xAxisData = dataToShow?.map(item => {
      const date = new Date(item.statKey);
      return `${date.getMonth() + 1}-${date.getDate()}`;
    });

    const avgScoreData = dataToShow?.map(item => item.avgScore);
    const problemProbabilityData = dataToShow?.map(item => item.problemProbability * 100);

    return {
      xAxisData,
      avgScoreData,
      problemProbabilityData,
    };
  }

  // 获取总页数
  getTotalPages() {
    const { dailyStats = [] } = this.props;
    const { pageSize } = this.state;
    return Math.ceil(dailyStats?.length / pageSize);
  }

  // 下一页（循环分页）
  handleNextPage = () => {
    const { currentPage } = this.state;
    const totalPages = this.getTotalPages();

    if (currentPage < totalPages) {
      this.setState({ currentPage: currentPage + 1 });
    } else {
      // 如果是最后一页，回到第一页
      this.setState({ currentPage: 1 });
    }
  };

  // 检查是否需要显示分页（只有本月才需要分页）
  shouldShowPagination() {
    const { selectedTime, dailyStats = [] } = this.props;
    const { pageSize } = this.state;

    // 只有本月且数据量大于pageSize时才显示分页
    return selectedTime === 'MONTH' && dailyStats?.length > pageSize;
  }

  handleResize() {
    if (this.chartInstance) {
      this.chartInstance.resize();
    }
  }

  renderText = () => {
    const { selectedTime, selectedRangeTime } = this.props;
    const range = {
      '0': '本',
      '-1': '上',
      '-2': '上上',
    };
    const time = {
      MONTH: '月',
      WEEK: '周',
    };
    return `${range[selectedRangeTime]}${time[selectedTime]}质检一览`;
  };

  initChart() {
    if (this.chartRef.current) {
      const { xAxisData, avgScoreData, problemProbabilityData } = this.getChartData();

      this.chartInstance = echarts.init(this.chartRef.current);
      this.chartInstance.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter(params) {
            let result = `${params[0].name}<br/>`;
            params.forEach(param => {
              if (param.seriesName === '平均得分数') {
                result += `${param.marker + param.seriesName}: ${param.value}分<br/>`;
              } else {
                result += `${param.marker + param.seriesName}: ${param.value.toFixed(0)}%<br/>`;
              }
            });
            return result;
          },
        },
        legend: {
          data: ['平均得分数', '红线病历段出现率'],
          right: 40,
          top: 10,
        },
        grid: {
          left: 40,
          right: 40,
          bottom: 30,
          top: 50,
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisTick: { alignWithLabel: true },
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 120,
          splitLine: { show: true },
        },
        series: [
          {
            name: '平均得分数',
            type: 'bar',
            data: avgScoreData,
            barWidth: 20,
            itemStyle: { color: '#1890ff' },
          },
          {
            name: '红线病历段出现率',
            type: 'bar',
            data: problemProbabilityData,
            barWidth: 20,
            itemStyle: { color: '#7ee787' },
          },
        ],
      });
    }
  }

  render() {
    const showPagination = this.shouldShowPagination();

    return (
      <div className={styles.overviewCard}>
        <div className={styles.cardHeader}>
          <span className={styles.cardTitle}>{this.renderText()}</span>
          <span
            className={styles.cardExtra}
            onClick={() => router.push('/CaseQualityInspection/CaseQualityCheck')}
          >
            查看详情 &gt;
          </span>
        </div>
        <div className={styles.cardBody}>
          <div ref={this.chartRef} className={styles.overviewChart} />
          {showPagination && (
            <div className={styles.paginationContainer}>
              <img src={nextIcon} alt="" onClick={this.handleNextPage} />
            </div>
          )}
        </div>
      </div>
    );
  }
}

export default Overview;
