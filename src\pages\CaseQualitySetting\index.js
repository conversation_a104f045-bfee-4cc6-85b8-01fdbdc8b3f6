/**
 * @Description: 病历质检设置
 * @author: sy
 */
import React, { Component } from 'react';
import { connect } from 'dva';
import { Card, Select, Table, Switch, Button, InputNumber } from 'antd';
import styles from './index.less';

const { Option } = Select;

@connect(({ loading, caseQualitySetting }) => ({
  loading,
  caseQualitySetting,
}))
class CaseQualitySetting extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // 基础设置
      evaluationCycle: '北京市标准', // 评估周期

      // 质检评分配置数据
      scoreConfigData: [
        {
          key: '1',
          category: '主诉',
          ruleDescription: '未填写主诉',
          score: 10,
          status: true,
          children: [
            {
              key: '1-1',
              category: '',
              ruleDescription: '自诊症主诉，需填写完整的症状',
              score: 5,
              status: true,
            },
            {
              key: '1-2',
              category: '',
              ruleDescription: '自诊症主诉，需填写主要症状',
              score: 5,
              status: false,
            },
            {
              key: '1-3',
              category: '',
              ruleDescription: '自诊症主诉，需填写完整病史（既往病史）',
              score: 5,
              status: true,
            },
          ],
        },
        {
          key: '2',
          category: '现病史',
          ruleDescription: '未填写现病史',
          score: 10,
          status: true,
          children: [
            {
              key: '2-1',
              category: '',
              ruleDescription: '病史主诉不详（主诉病）病史的发生',
              score: 3,
              status: true,
            },
            {
              key: '2-2',
              category: '',
              ruleDescription: '病史主诉不详（主诉病）病史的发展',
              score: 3,
              status: true,
            },
            {
              key: '2-3',
              category: '',
              ruleDescription: '病史主诉不详（主诉病）病史的治疗',
              score: 3,
              status: false,
            },
            {
              key: '2-4',
              category: '',
              ruleDescription: '病史主诉不详（主诉病）病史的治疗',
              score: 3,
              status: true,
            },
            {
              key: '2-5',
              category: '',
              ruleDescription: '病史主诉不详（主诉病）病史的相关症状',
              score: 3,
              status: true,
            },
          ],
        },
      ],

      // 学科红线规则
      redlineRules: [
        {
          key: '1',
          rule: '治疗药品的药品（医嘱药品共同使用的，退药、药品及药品处方）必须要求',
          status: true,
        },
        {
          key: '2',
          rule: '医疗器械设备及其配件的药品及其配件分析',
          status: true,
        },
        {
          key: '3',
          rule: '正确的药品、医疗工具及治疗药品的，医生的治疗药品的治疗药品及其配件的治疗药品及其配件',
          status: false,
        },
        {
          key: '4',
          rule: '药品正确的诊疗器械设备的药品及其配件的治疗药品及其配件',
          status: true,
        },
      ],

      // 学科选择
      selectedSubject: '妇科',
    };
  }

  componentDidMount() {
    // 初始化数据
    this.getSettingData();
  }

  // 获取设置数据
  getSettingData = () => {
    // 这里可以调用API获取数据
    // const { dispatch } = this.props;
    // dispatch({ type: 'caseQualitySetting/getSettingData' });
  };

  // 保存设置
  handleSave = () => {
    // 这里可以调用API保存数据
    // const { dispatch } = this.props;
    // dispatch({ type: 'caseQualitySetting/saveSettingData', payload: this.state });
  };

  // 处理评估周期变化
  handleEvaluationCycleChange = (value) => {
    this.setState({
      evaluationCycle: value,
    });
  };

  // 处理学科选择变化
  handleSubjectChange = (value) => {
    this.setState({
      selectedSubject: value,
    });
  };

  // 处理评分配置状态变化
  handleScoreStatusChange = (key, checked) => {
    const { scoreConfigData } = this.state;
    const updateData = (data) => {
      return data.map(item => {
        if (item.key === key) {
          return { ...item, status: checked };
        }
        if (item.children) {
          return { ...item, children: updateData(item.children) };
        }
        return item;
      });
    };

    this.setState({
      scoreConfigData: updateData(scoreConfigData),
    });
  };

  // 处理评分变化
  handleScoreChange = (key, value) => {
    const { scoreConfigData } = this.state;
    const updateData = (data) => {
      return data.map(item => {
        if (item.key === key) {
          return { ...item, score: value };
        }
        if (item.children) {
          return { ...item, children: updateData(item.children) };
        }
        return item;
      });
    };

    this.setState({
      scoreConfigData: updateData(scoreConfigData),
    });
  };

  // 处理红线规则状态变化
  handleRedlineStatusChange = (key, checked) => {
    const { redlineRules } = this.state;
    const newRules = redlineRules.map(rule => {
      if (rule.key === key) {
        return { ...rule, status: checked };
      }
      return rule;
    });

    this.setState({
      redlineRules: newRules,
    });
  };

  // 质检评分配置表格列定义
  getScoreConfigColumns = () => {
    return [
      {
        title: '类别',
        dataIndex: 'category',
        key: 'category',
        width: 100,
        render: (text, record) => {
          // 只在父级显示类别名称
          return record.category || '';
        },
      },
      {
        title: '规则描述',
        dataIndex: 'ruleDescription',
        key: 'ruleDescription',
        width: 400,
      },
      {
        title: '扣分值',
        dataIndex: 'score',
        key: 'score',
        width: 120,
        render: (text, record) => (
          <InputNumber
            min={0}
            max={100}
            value={text}
            onChange={(value) => this.handleScoreChange(record.key, value)}
            size="small"
            style={{ width: 80 }}
          />
        ),
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        render: (text, record) => (
          <div className={styles.switchWrapper}>
            <Switch
              checked={text}
              onChange={(checked) => this.handleScoreStatusChange(record.key, checked)}
              size="small"
            />
            <span className={styles.switchText}>{text ? '启用' : '禁用'}</span>
          </div>
        ),
      },
    ];
  };

  render() {
    const {
      evaluationCycle,
      scoreConfigData,
      redlineRules,
      selectedSubject,
    } = this.state;
    const { loading } = this.props;

    return (
      <div className={styles.settingContainer}>
        <div className={styles.header}>
          <h2>设置</h2>
          <Button type="primary" onClick={this.handleSave}>
            保存设置
          </Button>
        </div>

        {/* 基础设置 */}
        <Card className={styles.settingCard} title="基础设置" size="small" bordered={false}>
          <div className={styles.settingItem}>
            <span className={styles.settingLabel}>评估周期</span>
            <span className={styles.settingDesc}>配置质检评分的评估周期</span>
          </div>
          <div className={styles.settingContent}>
            <span className={styles.fieldLabel}>评估周期</span>
            <Select
              value={evaluationCycle}
              onChange={this.handleEvaluationCycleChange}
              style={{ width: 200 }}
              size="small"
            >
              <Option value="北京市标准">北京市标准</Option>
              <Option value="上海市标准">上海市标准</Option>
              <Option value="广州市标准">广州市标准</Option>
            </Select>
          </div>
        </Card>

        {/* 质检评分配置 */}
        <Card className={styles.settingCard} title="质检评分配置" size="small" bordered={false}>
          <div className={styles.settingItem}>
            <span className={styles.settingDesc}>配置各项质检的评分标准和扣分规则</span>
          </div>
          <div className={styles.tableWrapper}>
            <Table
              columns={this.getScoreConfigColumns()}
              dataSource={scoreConfigData}
              pagination={false}
              size="small"
              expandable={{
                defaultExpandAllRows: true,
                indentSize: 20,
              }}
              rowKey="key"
              className={styles.scoreConfigTable}
            />
          </div>
        </Card>

        {/* 学科红线规则 */}
        <Card className={styles.settingCard} title="学科红线规则" size="small" bordered={false}>
          <div className={styles.settingItem}>
            <span className={styles.settingDesc}>为不同学科配置红线检查规则的启用状态</span>
          </div>
          <div className={styles.settingContent}>
            <span className={styles.fieldLabel}>选择学科</span>
            <Select
              value={selectedSubject}
              onChange={this.handleSubjectChange}
              style={{ width: 200 }}
              size="small"
            >
              <Option value="妇科">妇科</Option>
              <Option value="内科">内科</Option>
              <Option value="外科">外科</Option>
              <Option value="儿科">儿科</Option>
            </Select>
          </div>

          <div className={styles.redlineRules}>
            <div className={styles.rulesHeader}>
              <span className={styles.rulesTitle}>红线规则</span>
              <span className={styles.rulesTitle}>状态</span>
            </div>
            {redlineRules.map(rule => (
              <div key={rule.key} className={styles.ruleItem}>
                <div className={styles.ruleText}>{rule.rule}</div>
                <div className={styles.ruleStatus}>
                  <Switch
                    checked={rule.status}
                    onChange={(checked) => this.handleRedlineStatusChange(rule.key, checked)}
                    size="small"
                  />
                  <span className={styles.switchText}>{rule.status ? '启用' : '禁用'}</span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    );
  }
}

export default CaseQualitySetting;