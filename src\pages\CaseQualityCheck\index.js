/**
 * 病历质检 -- 电子病历质量检查
 */

import React, { Component } from 'react';
import { connect } from 'dva';
import { Table, Spin } from 'antd';
// 暂无数据组件
import DataFailure from '@/components/DataFailure';
// 头部组件
import Header from './components/Header';
import styles from './index.less';

// 表格列定义
const columns = [
  {
    title: '病历提交日期',
    dataIndex: 'submitTime',
    key: 'submitTime',
    align: 'center',
    width: 200,
  },
  { title: '病历号', dataIndex: 'emrId', key: 'emrId', align: 'center', width: 150 },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    key: 'patientName',
    align: 'center',
    width: 150,
  },
  {
    title: '接诊医生',
    dataIndex: 'doctorName',
    key: 'doctorName',
    align: 'center',
    width: 150,
  },
  { title: '质量检查评分', dataIndex: 'score', key: 'score', align: 'center', width: 150 },
  {
    title: '红线问题检出数',
    dataIndex: 'problemNum',
    key: 'problemNum',
    align: 'center',
    render: text => <span style={{ color: text > 0 ? '#FF4D4F' : undefined }}>{text}</span>,
  },
  {
    title: '操作',
    key: 'action',
    align: 'center',
    fixed: 'right',
    width: 120,
    render: row => (
      <a href={`/CaseQualityDetail/${row.emrSubId}`} target="_blank" rel="noopener noreferrer">
        {' '}
        查看详情{' '}
      </a>
    ),
  },
];

@connect(({ loading, caseQualityCheck }) => ({
  loading,
  caseQualityCheck,
}))
class CaseQualityCheck extends Component {
  state = {
    pageNum: 1,
    pageSize: 10,
    total: 0,
    codeType: 1, // 0 数据加载失败  1 暂无数据
    dataSource: [],
    dictData: [],
  };

  componentDidMount() {
    this.initializeTimeRange();
    this.getFilterDict();
  }

  // 初始化时间范围
  initializeTimeRange = () => {
    const { dispatch } = this.props;
    const selectedTime = localStorage.getItem('caseQualityInspectionSelectedTime');
    const selectedRange 
    const dateCleared = localStorage.getItem('caseQualityCheckDateCleared');

    // 如果用户已经手动清除过时间，则不设置默认时间
    if (dateCleared === 'true') {
      this.getList();
      return;
    }

    let submitTimeFrom = '';
    let submitTimeTo = '';

    if (selectedTime === 'MONTH') {
      // 获取当前自然月的第一天和最后一天
      const now = new Date();
      const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
      const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      submitTimeFrom = this.formatDate(firstDay);
      submitTimeTo = this.formatDate(lastDay);
    } else if (selectedTime === 'WEEK') {
      // 获取当前周的周一和周日
      const now = new Date();
      const dayOfWeek = now.getDay();
      const monday = new Date(now);
      const sunday = new Date(now);

      // 计算周一（如果今天是周日，dayOfWeek为0，需要特殊处理）
      const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
      monday.setDate(now.getDate() + daysToMonday);

      // 计算周日
      const daysToSunday = dayOfWeek === 0 ? 0 : 7 - dayOfWeek;
      sunday.setDate(now.getDate() + daysToSunday);

      submitTimeFrom = this.formatDate(monday);
      submitTimeTo = this.formatDate(sunday);
    }

    // 如果有时间范围，设置到store并获取数据
    if (submitTimeFrom && submitTimeTo) {
      dispatch({
        type: 'caseQualityCheck/setScreenState',
        payload: {
          submitTimeFrom,
          submitTimeTo,
        },
      });
      // 使用setTimeout确保state更新后再获取数据
      setTimeout(() => {
        this.getList();
      }, 0);
    } else {
      // 没有默认时间范围时直接获取数据
      this.getList();
    }
  };

  // 格式化日期为 YYYY-MM-DD 格式
  formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // 获取列表数据
  getList = () => {
    const { dispatch, caseQualityCheck } = this.props;
    const { searchKey, submitTimeFrom, submitTimeTo, doctorUserId } = caseQualityCheck || {};

    const { pageNum, pageSize } = this.state;
    dispatch({
      type: 'caseQualityCheck/getList',
      payload: {
        submitTimeFrom: submitTimeFrom ? `${submitTimeFrom} 00:00:00` : '', // 日期--开始
        submitTimeTo: submitTimeTo ? `${submitTimeTo} 23:59:59` : '', // 日期--结束
        doctorUserId, // 医生
        searchKey, // 搜索关键字
        pageNum, // 页码
        pageSize, // 条数
        tenantId: localStorage.getItem('tenantId'),
        organizationId: localStorage.getItem('organizationInfoId'),
      },
    })
      .then(res => {
        const { code, data } = res || {};
        const { total, resultList } = data || {};
        if (code === 200) {
          if (Array.isArray(resultList) && resultList.length) {
            this.setState({
              dataSource: resultList,
              total,
            });
          } else {
            this.setState({
              dataSource: [],
              codeType: 1,
            });
          }
        } else {
          this.setState({
            dataSource: [],
            codeType: 0,
          });
        }
      })
      .catch(err => {
        this.setState({
          dataSource: [],
          codeType: 0,
        });
        console.log(err);
      });
  };

  // 获取筛选字典
  getFilterDict = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'caseQualityCheck/getFilterDict',
      payload: {},
    })
      .then(res => {
        const { code, content, msg } = res || {};
        if (code === 200) {
          this.setState({
            dictData: content,
          });
        }
      })
      .catch(err => {
        console.log(err);
      });
  };

  // 搜索、筛选刷新接口
  refreshInterface = () => {
    this.setState(
      {
        pageNum: 1,
      },
      () => {
        this.getList();
      }
    );
  };

  /**
   * 分页改变
   * @param {*} current   当前页
   */
  paginationChange = current => {
    this.setState(
      {
        pageNum: current,
      },
      () => {
        this.getList();
      }
    );
  };

  /**
   * 当前页面条数改变
   * @param {*} current
   * @param {*} pageSize   // 每页多少条
   */
  onShowSizeChange = (current, pageSize) => {
    this.setState(
      {
        pageNum: 1, // 页码
        pageSize, // 数据条数
      },
      () => {
        this.getList();
      }
    );
  };

  render() {
    const { dataSource, pageNum, pageSize, total, codeType, dictData } = this.state;
    const { loading } = this.props;

    return (
      <Spin spinning={!!loading.effects['caseQualityCheck/getList']}>
        <div className={styles.caseQualityCheckWrap}>
          <Header dictData={dictData} refreshInterface={this.refreshInterface} />
          <div className={styles.content}>
            <div className={styles.content_table}>
              <Table
                columns={columns}
                dataSource={dataSource}
                scroll={{
                  x: 1070,
                  y: 'calc(100vh - 330px)',
                  scrollToFirstRowOnChange: true,
                }}
                rowKey={record => record.id}
                pagination={{
                  showSizeChanger: true,
                  onShowSizeChange: this.onShowSizeChange,
                  onChange: this.paginationChange,
                  pageSize,
                  current: pageNum,
                  pageSizeOptions: ['10', '20', '30', '40', '50'],
                  total,
                }}
                locale={{
                  emptyText: (
                    <DataFailure
                      DataFailMsg={
                        codeType == 1 ? '暂无数据' : codeType == 0 ? '数据加载失败' : null
                      }
                      CodeType={codeType}
                      key={codeType !== null ? `${codeType}` : 'caseQualityCheck'}
                    />
                  ),
                }}
              />
            </div>
          </div>
        </div>
      </Spin>
    );
  }
}

export default CaseQualityCheck;
